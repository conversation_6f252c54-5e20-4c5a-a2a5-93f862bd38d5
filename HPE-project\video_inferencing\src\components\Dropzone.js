// TODO:
// Add multiple video files in list and process for all of them.
// Maybe set cap to 5

import React, { useState, useRef } from 'react';
import '../css/dropzone.css';

function DropZone({ onFileChange }) {

    const Ref = useRef(null);
    const [fileName, setFileName] = useState(null);

    const onDragEnter = () => {
        Ref.current.classList.add('dragover');
    }
    const onDragLeave = () => {
        Ref.current.classList.remove('dragover');
    }
    const onDrop = () => {
        Ref.current.classList.remove('dragover');
    }
    const onFileDrop = (e) => {
        const newFile = e.target.files[0];
        if(newFile){
            setFileName(newFile.name);
            onFileChange(newFile);
        }
    }

    const fileRemove = (file) => {
        // shud do
    }

    return (
        <>
            <div
                ref={Ref}
                className='dropzone'
                onDragEnter={onDragEnter}
                onDragLeave={onDragLeave}
                onDrop={onDrop}
            >
                <div className='dropzone__prompt'>
                    <span>Drag & Drop files here or click to select</span>
                </div>

                <input type='file' value="" onChange={onFileDrop} />
            </div>
            {
                fileName ? (
                    <div className='dropzone__file'>
                        <span>{fileName}</span>
                        {/* Remove option / Delete */}
                    </div>
                ) : null
            }
        </>
    )

}

export default DropZone;