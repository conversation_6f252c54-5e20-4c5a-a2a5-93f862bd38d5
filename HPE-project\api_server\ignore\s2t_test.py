import whisper_s2t
import time
import gc
import torch
from multiprocessing import Process
import json

def clean_transcription_data(data):
    cleaned_data = []
    
    for item in data:
        cleaned_item = {
            key: value for key, value in item.items() 
            if key not in ['avg_logprob', 'no_speech_prob']
        }
        cleaned_data.append(cleaned_item)
    
    return cleaned_data

def merge_result(transcription_data):
    merged_text = ""
    
    for x in transcription_data[0]:
        if 'text' in x:
            if merged_text:
                merged_text += " "
            merged_text += x['text']
    
    return merged_text.strip()

def run_transcription():

    device = "cuda"
    compute_type = "float16"
    batch_size = 8

    model = whisper_s2t.load_model("small", backend="CTranslate2", device=device, compute_type=compute_type, max_speech_len=4.0) # max_speech_len=2.0 for better timestamps but worsens performance

    files = [r"cillian.mp3"]
    lang_codes = ["en"]
    tasks = ["transcribe"]
    initial_prompts = [None]

    start = time.time()

    out = model.transcribe_with_vad(files, lang_codes=lang_codes, tasks=tasks, initial_prompts=initial_prompts, batch_size=batch_size)

    whisper_s2t.write_outputs(out, format='vtt', op_files=["output.txt"])
    whisper_s2t.write_outputs(out, format='json', op_files=["output.json"])

    with open("output.json", "r") as f:
        data = json.load(f)
    
    cleaned_data = clean_transcription_data(data)
    json.dump(cleaned_data, open("output.json", "w"), indent=4)

    res = open('output.txt', 'r').read()[6:]

    print(res)

    end = time.time()

    print(f"\n time taken for transcription = {end - start}")

    gc.collect()
    torch.cuda.empty_cache()
    del model

if __name__ == "__main__":
    p = Process(target=run_transcription)
    p.start()
    p.join()