WEBVTT

00:00.040 --> 00:27.640
Hello friends, I am going to start a course on algorithms. Algorithm as a subject. Algorithm is a common subject for computer science engineering students. Most of the universities offer this course as a part of syllabus and this is a very core subject and very important subject. And students face some difficulties in some of the topics in this one. They could not understand them very clearly.

00:28.700 --> 00:57.620
So here I am going to make the subject more and more simple so that you can easily understand and even you can practice that and also remember it easily. The importance of the subject is that apart from theoretical examination, it is also important for competitive exams. And even programming contest, most of them are designed from this subject only. If any programming challenge is there, then mostly they pick up the questions from this subject.

00:58.380 --> 01:24.560
So this is one of the challenging subject for the student because they get a chance to solve different type of problems and they get the strategy or approach for solving the problem. So some students fail to understand it properly so they could not get the approach or the strategy for solving the problems and they feel that they are lacking in the logic development or strategic development of a program.

01:25.480 --> 01:54.040
So I will be covering in depth each and everything right from the basics to the depth. So that you will be able to answer any type of question from this one. I will cover the topics such that just you look at the question you can get the answer that I guarantee you. So the thing is I will cover those points which are useful for solving the questions.

01:55.920 --> 02:22.340
Some job interviews or even for any other exam or some entrance exams like gate exam, the cushions are framed from algorithms also. There is also one of the key subject there. So you will be able to answer any type of question from there. Now towards the lectures in the order, I'll be giving the numbers for each topic so that you follow that order, you follow the sequence because I may be breaking the major topic into small topics.

02:23.440 --> 02:47.960
And one more important thing is your feedback is very important for me because already I have made few prepared few videos using presentations and other tools but now I am giving a lecture on whiteboard so maybe I am new for the video quality and audio quality all these things so technically once I get set up so I need your feedback so I can make my videos more and more better

02:48.300 --> 03:11.860
So your feedback is important for me to know how the video quality and audio quality you are getting it on your devices because I have checked on different devices and I found that the results are different. So what devices you are using, what I should improve, voice or what so I need your feedback. Let us start with just introduction. What is an Elcortone?

03:12.560 --> 03:43.080
What is an algorithm? Why it is written? When it is written? Who will write it? Let us get the answers for these. See, algorithm as a definition, it's a common definition everybody knows that is a step by step procedure for solving a computational problem. Yes, it is a step by step procedure for solving a computational problem. Then what is a program? Program is also a step by step procedure for solving a problem. Then what is the difference between program and algorithm?

03:43.560 --> 04:22.120
So by comparing program and algorithm I can make you understand the importance of algorithm, meaning of algorithm. Let us take a look. Here is an algorithm and here is a program. Let us see the first thing, first difference. If you know the software development life cycle, means the phases of developing a software project,

04:22.520 --> 04:44.880
In the phases of development of software projects there are two important phases, design phase and implementation phase. If you are not aware of it let me tell you whatever you want to manufacture or construct something by engineering procedures then first step one of the important step is designing. First you do designing

04:45.280 --> 05:02.040
Make your design perfect and thorough so that you can understand what you are going to construct, what you are going to develop and when you are sure what you are going to develop then you start to develop. You can't develop anything, you cannot construct anything on trial and error basis.

05:02.480 --> 05:30.700
Like you constructed something, again no one is wrong, destroyed and again created a new one. So no. But it is easy in software engineering. Software engineer can write some program and change the mind, delete the program and again start writing them. So that's why we cannot get the feel that we have wasted so much amount of time in writing so useless program. So the point is first you design and then you write the program.

05:31.500 --> 05:59.580
So at the design time what do you use? So you are not writing a program then what do you write? So that same program we write it in simple English like statements that are easy to understand without using proper syntax and we may not be writing on machine that is on computer, we may be writing on paper or even if you are using a machine also then you will not be writing in some language, you may be writing in MS Word or notepad like application.

05:59.920 --> 06:39.980
So just you are getting familiar how your program is going to work. So that's nothing but an algorithm. So algorithms are written at design time. And when the programs are written, they are written at implementation time, design time and implementation. So first you make a design what your procedure is going to do, what your software is going to do, come up with some design and that design you convert it to a program.

06:41.500 --> 07:03.440
Then what do you call the person who do designing? Whether programmer does that? Yes, if programmer is capable of doing that he can do it. Otherwise the person who will do this one should have the domain knowledge, the problems knowledge, knowledge about the problem and its solution. He can give a solution. The one who understand the problem

07:03.720 --> 07:25.640
Suppose you are developing a software or an algorithm or a program for account then accountant can understand accounting better. Or if you are writing an application or developing an application for hospital then doctors or the administrative staff of a hospital can understand the system better than a programmer.

07:25.900 --> 08:02.840
So mostly the one who is having a knowledge about the domain for which the application is being developed, they can write on the algorithm. So the person who writes should have domain knowledge. Who will write the programmer? Programmer? So programmers can also have domain knowledge. They can also have domain knowledge. He is acting as a designer and here he is acting as a programmer.

08:03.720 --> 08:30.700
Next, what is the language used for writing Al-Qaardam? You can use any language. Any language. Any language means English like language or you can use some mathematical notations. If you are writing English like language it will be like writing Paras or Samari. But don't try it. If you want you can use mathematical notations also. It's better if you use mathematical notations.

08:31.880 --> 09:01.960
So any language can be used, any language means English language or some mathematical notations can be used as long as it is understandable by those people who are using it. Like if a designer has written an algorithm then the programmer will be writing a program for it. So designers should understand and also programmers should understand that one. So the team of programmers who are working on that project should be able to understand it. Then this is written only using programming language.

09:04.220 --> 09:38.700
like cc++, java, python, right? So you can use different languages for developing a program. The next one more important thing here when you write an L-Corn it's going to be hardware and software means operating system independent. It's not dependent on hardware what machine you are going to use what's the configuration of the machine and what's the operating system either the Linux operating system or Windows operating system we don't bother about it.

09:39.020 --> 10:02.900
But when you write a program, it is dependent on hardware and operating system. So when you develop a program, you have to select some hardware on which you are going to run. And also you have to select some operating system. You may be doing it for Linux or you may be doing it for Windows. So the method will be different and the environments are different. So this is important.

10:04.600 --> 10:38.760
Then next, after writing an algorithm we analyze, analyze an algorithm. Means we will study the algorithm to find out are we achieving the results perfectly or not and our algorithm is efficient or not in terms of time and space. We will see what does it mean by analysis. So we will analyze an algorithm. What do we do with the program? So you don't have to study the program. Already program is there. Just run it and check it.

10:39.140 --> 11:14.720
So we do testing, testing of the program. That's it. These are few differences between algorithm and program. These differences will help you understand what are algorithms. All right? Now one more thing I will tell you. The syntax of the language that we use, any language can be used but who actually write algorithms and who use them and who write the programs of programmers who are coming from university

11:15.160 --> 11:47.420
mostly university graduate knows at least C language. So nowadays, mostly in the name of algorithm we write C language program only. So the benefit is that the advantage is that everybody knows C language now. So instead of writing some other language to confuse, we can use C language only so that everybody can understand. So even at a school level C language is being taught so everybody is familiar with C language. So I may be using C language syntax for writing algorithm.

