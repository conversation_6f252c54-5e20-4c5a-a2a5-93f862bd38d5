Two environments cuz of dependency issues (faiss and tts)

#### In one terminal
```bash
python -m venv server_env # transcription and tts

server_env\Scripts\activate.bat

pip install -r server_requirements.txt

python server.py # in server_env
```

#### In another terminal
```bash
python -m venv faiss_env # faiss and chat with ollama model

faiss_env\Scripts\activate.bat

pip install -r faiss_backend_requirements.txt
```

```bash
uvicorn faiss_backend:app --port 8001 --reload
```

#### In another terminal
```bash
ollama serve
```

#### Audio outputs
stored in `audio_output/`