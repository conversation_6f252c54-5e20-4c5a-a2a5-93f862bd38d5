"""
LLM Model Comparison Analysis Tool

This script analyzes the outputs from the LLM comparison and creates
comprehensive graphs highlighting performance differences.
"""

import json
import os
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from nltk.tokenize import word_tokenize
import nltk
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import time

# Ensure NLTK data is downloaded
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    print("Downloading NLTK punkt data...")
    nltk.download('punkt')

# Manually ensure punkt is available
if not os.path.exists(os.path.join(nltk.data.find('tokenizers'), 'punkt')):
    print("Forced download of NLTK punkt data...")
    nltk.download('punkt', quiet=False)

# Constants
RESULTS_FILE = "comparison_results.json"
OUTPUT_DIR = "analysis_output"

# Create output directory if it doesn't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)

def load_results(file_path=RESULTS_FILE):
    """Load comparison results from JSON file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading results: {str(e)}")
        return None

def calculate_response_metrics(results):
    """Calculate various metrics from the responses"""
    metrics = []
    
    if not results:
        return pd.DataFrame()
    
    all_responses = [r["response"] for r in results["results"]]
    
    # Create TF-IDF matrix for similarity comparison
    vectorizer = TfidfVectorizer()
    try:
        tfidf_matrix = vectorizer.fit_transform(all_responses)
        similarity_matrix = cosine_similarity(tfidf_matrix)
    except:
        # Handle empty responses
        similarity_matrix = np.zeros((len(all_responses), len(all_responses)))
    
    for i, result in enumerate(results["results"]):
        # Basic metrics
        response_length = len(result["response"])
        # Simple word count as fallback if NLTK is not working
        try:
            word_count = len(word_tokenize(result["response"]))
        except:
            # Fallback to simple splitting
            word_count = len(result["response"].split())
        
        # Calculate average similarity with other responses
        similarities = []
        for j in range(len(all_responses)):
            if i != j:
                similarities.append(similarity_matrix[i][j])
        
        avg_similarity = np.mean(similarities) if similarities else 0
        
        metrics.append({
            "model": result["model"],
            "model_id": result.get("model_id", result["model"]),
            "execution_time": result["execution_time"],
            "token_count": result["token_count"],
            "response_length": response_length,
            "word_count": word_count,
            "avg_similarity": avg_similarity,
            "success": result["success"]
        })
    
    return pd.DataFrame(metrics)

def plot_execution_time(df, output_dir=OUTPUT_DIR):
    """Plot execution time comparison"""
    plt.figure(figsize=(10, 6))
    
    # Create a bar chart with updated parameters to avoid FutureWarning
    ax = sns.barplot(x='model_id', y='execution_time', hue='model_id', data=df, palette='viridis', legend=False)
    
    # Add labels and title
    plt.xlabel('Model')
    plt.ylabel('Execution Time (seconds)')
    plt.title('LLM Model Execution Time Comparison')
    
    # Add the execution time values on top of each bar
    for i, v in enumerate(df['execution_time']):
        ax.text(i, v + 0.1, f"{v:.2f}s", ha='center')
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'execution_time_comparison.png'))
    plt.close()

def plot_response_length_comparison(df, output_dir=OUTPUT_DIR):
    """Plot response length comparison"""
    plt.figure(figsize=(12, 6))
    
    # Prepare data
    df_melted = df.melt(id_vars=['model_id'], 
                         value_vars=['response_length', 'word_count', 'token_count'],
                         var_name='Metric', value_name='Count')
    
    # Create a grouped bar chart
    ax = sns.barplot(x='model_id', y='Count', hue='Metric', data=df_melted, palette='coolwarm')
    
    # Add labels and title
    plt.xlabel('Model')
    plt.ylabel('Count')
    plt.title('LLM Response Size Comparison')
    plt.legend(title='Metric')
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'response_size_comparison.png'))
    plt.close()

def plot_similarity_heatmap(df, results, output_dir=OUTPUT_DIR):
    """Plot similarity heatmap between model responses"""
    responses = [r["response"] for r in results["results"]]
    model_names = df['model_id'].tolist()
    
    # Create TF-IDF matrix
    vectorizer = TfidfVectorizer()
    try:
        tfidf_matrix = vectorizer.fit_transform(responses)
        similarity_matrix = cosine_similarity(tfidf_matrix)
    except:
        # Handle empty responses
        similarity_matrix = np.zeros((len(responses), len(responses)))
    
    # Create heatmap
    plt.figure(figsize=(10, 8))
    sns.heatmap(similarity_matrix, annot=True, cmap='YlGnBu', xticklabels=model_names, yticklabels=model_names)
    plt.title('Response Similarity Between Models (Cosine Similarity)')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'similarity_heatmap.png'))
    plt.close()

def plot_performance_radar(df, output_dir=OUTPUT_DIR):
    """Create a radar chart for model performance"""
    # Normalize metrics to 0-1 scale for radar chart
    metrics = ['execution_time', 'response_length', 'word_count', 'token_count']
    
    # Invert execution time so lower is better (higher on radar)
    df_radar = df.copy()
    max_exec_time = df_radar['execution_time'].max()
    df_radar['execution_time_inv'] = 1 - (df_radar['execution_time'] / max_exec_time)
    
    # Normalize other metrics
    for metric in ['response_length', 'word_count', 'token_count']:
        max_val = df_radar[metric].max()
        if max_val > 0:
            df_radar[f"{metric}_norm"] = df_radar[metric] / max_val
        else:
            df_radar[f"{metric}_norm"] = 0
    
    # Radar chart metrics
    radar_metrics = ['execution_time_inv', 'response_length_norm', 'word_count_norm', 'token_count_norm']
    radar_labels = ['Speed', 'Response Length', 'Word Count', 'Token Count']
    
    # Create radar chart
    angles = np.linspace(0, 2*np.pi, len(radar_metrics), endpoint=False).tolist()
    angles += angles[:1]  # Close the loop
    
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True))
    
    for i, row in df_radar.iterrows():
        values = [row[m] for m in radar_metrics]
        values += values[:1]  # Close the loop
        
        ax.plot(angles, values, linewidth=2, label=row['model_id'])
        ax.fill(angles, values, alpha=0.1)
    
    # Set labels and styling
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(radar_labels)
    ax.set_yticks([0.25, 0.5, 0.75, 1.0])
    ax.set_yticklabels(['0.25', '0.5', '0.75', '1.0'])
    
    plt.title('Model Performance Comparison', size=15)
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'performance_radar.png'))
    plt.close()

def create_summary_table(df, results, output_file):
    """Create a summary table with key metrics"""
    summary = df[['model_id', 'execution_time', 'token_count', 'word_count']].copy()
    
    # Add the query
    summary['query'] = results['query']
    
    # Add timestamp
    summary['timestamp'] = results['timestamp']
    
    # Round execution time to 2 decimal places
    summary['execution_time'] = summary['execution_time'].round(2)
    
    # Add estimated cost (placeholder - in real use, this would need actual API pricing)
    summary['estimated_cost'] = 0
    
    # Save to CSV
    summary.to_csv(output_file, index=False)
    
    return summary

def create_html_report(results, metrics_df, output_dir=OUTPUT_DIR):
    """Create an HTML report with all results and charts"""
    # Format the HTML
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>LLM Model Comparison Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; }}
            .charts {{ display: flex; flex-wrap: wrap; justify-content: space-around; margin-top: 20px; }}
            .chart {{ margin: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); border-radius: 5px; overflow: hidden; }}
            table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
            th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
            th {{ background-color: #f2f2f2; }}
            tr:hover {{ background-color: #f5f5f5; }}
            .response {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 10px; white-space: pre-wrap; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>LLM Model Comparison Report</h1>
            <p><strong>Query:</strong> {results["query"]}</p>
            <p><strong>Generated:</strong> {results["timestamp"]}</p>
        </div>

        <h2>Performance Metrics</h2>
        <table>
            <tr>
                <th>Model</th>
                <th>Execution Time (s)</th>
                <th>Token Count</th>
                <th>Word Count</th>
                <th>Success</th>
            </tr>
    """
    
    # Add each model's metrics to the HTML table
    for _, row in metrics_df.iterrows():
        success_text = "Yes" if row["success"] else "No"  # Use plain text instead of Unicode symbols
        html_content += f"""
            <tr>
                <td>{row["model_id"]}</td>
                <td>{row["execution_time"]:.2f}</td>
                <td>{row["token_count"]}</td>
                <td>{row["word_count"]}</td>
                <td>{success_text}</td>
            </tr>
        """
    
    html_content += """
        </table>

        <h2>Visualization Charts</h2>
        <div class="charts">
            <div class="chart">
                <img src="execution_time_comparison.png" alt="Execution Time Comparison">
            </div>
            <div class="chart">
                <img src="response_size_comparison.png" alt="Response Size Comparison">
            </div>
            <div class="chart">
                <img src="similarity_heatmap.png" alt="Similarity Heatmap">
            </div>
            <div class="chart">
                <img src="performance_radar.png" alt="Performance Radar">
            </div>
        </div>

        <h2>Model Responses</h2>
    """
    
    # Add each model's response
    for result in results["results"]:
        html_content += f"""
        <h3>{result["model_id"]} ({result["model"]})</h3>
        <p><strong>Execution Time:</strong> {result["execution_time"]:.2f} seconds</p>
        <div class="response">{result["response"]}</div>
        """
    
    html_content += """
    </body>
    </html>
    """
    
    # Write HTML to file with UTF-8 encoding to handle special characters
    with open(os.path.join(output_dir, 'report.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

def main():
    print("LLM Comparison Analysis Tool")
    print("=" * 50)
    
    # Load results
    results = load_results()
    if not results:
        print("No results file found. Please run the comparison script first.")
        return
    
    print(f"Analyzing results for query: {results['query']}")
    
    # Calculate metrics
    metrics_df = calculate_response_metrics(results)
    
    if metrics_df.empty:
        print("No valid metrics could be calculated.")
        return
    
    # Generate plots
    print("Generating performance plots...")
    plot_execution_time(metrics_df)
    plot_response_length_comparison(metrics_df)
    plot_similarity_heatmap(metrics_df, results)
    plot_performance_radar(metrics_df)
    
    # Create summary table
    summary = create_summary_table(metrics_df, results, os.path.join(OUTPUT_DIR, 'summary.csv'))
    print(f"Summary table created with {len(summary)} rows")
    
    # Create HTML report
    create_html_report(results, metrics_df)
    
    print(f"\nAnalysis complete! Results saved to {OUTPUT_DIR} directory.")
    print(f"Open {os.path.join(OUTPUT_DIR, 'report.html')} to view the complete report.")

if __name__ == "__main__":
    main()
