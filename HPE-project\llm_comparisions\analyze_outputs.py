"""
LLM Model Comparison Analysis Tool

This script analyzes the outputs from the model_comparison.py script and
creates visualizations comparing the performance of different LLM models.
"""

import json
import os
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.tokenize import sent_tokenize, word_tokenize
import re
import textwrap
from pathlib import Path
from datetime import datetime

# Ensure output directories exist
OUTPUT_DIR = "llm_outputs_images"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Download necessary NLTK resources
def download_nltk_resources():
    resources = ['punkt', 'vader_lexicon']
    for resource in resources:
        try:
            nltk.data.find(f'tokenizers/{resource}')
        except LookupError:
            print(f"Downloading NLTK resource: {resource}")
            nltk.download(resource, quiet=True)

def load_results(file_path="llm_outputs.json"):
    """Load comparison results from JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading results: {str(e)}")
        return None

def calculate_metrics(results):
    """Calculate various metrics from the model responses"""
    if not results:
        return pd.DataFrame()
    
    # Filter out results that have errors
    valid_results = [r for r in results if "error" not in r]
    
    if not valid_results:
        print("No valid results found.")
        return pd.DataFrame()
    
    # Download NLTK resources if needed
    download_nltk_resources()
    
    metrics = []
    all_responses = [r["response"] for r in valid_results]
    
    # Create TF-IDF matrix for similarity comparison
    vectorizer = TfidfVectorizer()
    try:
        tfidf_matrix = vectorizer.fit_transform(all_responses)
        similarity_matrix = cosine_similarity(tfidf_matrix)
    except Exception as e:
        print(f"Error creating similarity matrix: {str(e)}")
        similarity_matrix = np.zeros((len(all_responses), len(all_responses)))
    
    # Initialize sentiment analyzer
    try:
        sia = SentimentIntensityAnalyzer()
    except:
        print("Could not initialize sentiment analyzer. Sentiment metrics will be skipped.")
        sia = None
    
    for i, result in enumerate(valid_results):
        response_text = result["response"]
        
        # Basic metrics
        response_length = len(response_text)
        
        # Word count
        try:
            words = word_tokenize(response_text)
            word_count = len(words)
        except:
            word_count = len(response_text.split())
        
        # Sentence count
        try:
            sentences = sent_tokenize(response_text)
            sentence_count = len(sentences)
            avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
        except:
            sentence_count = 0
            avg_sentence_length = 0
        
        # Sentiment analysis
        sentiment_scores = {}
        if sia:
            try:
                sentiment = sia.polarity_scores(response_text)
                sentiment_scores = {
                    "sentiment_positive": sentiment["pos"],
                    "sentiment_negative": sentiment["neg"],
                    "sentiment_neutral": sentiment["neu"],
                    "sentiment_compound": sentiment["compound"]
                }
            except:
                sentiment_scores = {
                    "sentiment_positive": 0,
                    "sentiment_negative": 0,
                    "sentiment_neutral": 0,
                    "sentiment_compound": 0
                }
        
        # Calculate average similarity with other responses
        similarities = []
        for j in range(len(all_responses)):
            if i != j:
                similarities.append(similarity_matrix[i][j])
        
        avg_similarity = np.mean(similarities) if similarities else 0
        
        # Processing time and token efficiency
        processing_time = result.get("time_seconds", 0)
        tokens_used = result.get("tokens", {}).get("total", 0)
        completion_tokens = result.get("tokens", {}).get("completion", 0)
        
        # Token efficiency (output tokens per second)
        token_efficiency = completion_tokens / processing_time if processing_time > 0 else 0
        
        metrics.append({
            "model": result["model"],
            "model_api_name": result.get("model_api_name", "unknown"),
            "response_length": response_length,
            "word_count": word_count,
            "sentence_count": sentence_count,
            "avg_sentence_length": avg_sentence_length,
            "avg_similarity": avg_similarity,
            "processing_time": processing_time,
            "tokens_total": tokens_used,
            "tokens_completion": completion_tokens,
            "token_efficiency": token_efficiency,
            **sentiment_scores
        })
    
    return pd.DataFrame(metrics)

def plot_processing_time(df, output_dir=OUTPUT_DIR):
    """Plot processing time comparison"""
    plt.figure(figsize=(10, 6))
    
    # Sort by processing time
    df_sorted = df.sort_values("processing_time")
    
    # Create the bar chart
    ax = sns.barplot(x="model", y="processing_time", data=df_sorted, palette="viridis")
    
    # Add labels
    plt.xlabel("Model")
    plt.ylabel("Processing Time (seconds)")
    plt.title("LLM Model Processing Time Comparison")
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha="right")
    
    # Add values on top of bars
    for i, v in enumerate(df_sorted["processing_time"]):
        ax.text(i, v + 0.1, f"{v:.2f}s", ha="center")
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "processing_time_comparison.png"), dpi=300)
    plt.close()

def plot_token_metrics(df, output_dir=OUTPUT_DIR):
    """Plot token usage metrics"""
    plt.figure(figsize=(12, 6))
    
    # Sort by total tokens
    df_sorted = df.sort_values("tokens_total")
    
    # Create a grouped bar chart for token metrics
    df_tokens = df_sorted[["model", "tokens_total", "tokens_completion"]].copy()
    df_tokens = df_tokens.melt(
        id_vars=["model"], 
        value_vars=["tokens_total", "tokens_completion"],
        var_name="Token Type", 
        value_name="Token Count"
    )
    
    # Clean up names for legend
    df_tokens["Token Type"] = df_tokens["Token Type"].map({
        "tokens_total": "Total Tokens", 
        "tokens_completion": "Completion Tokens"
    })
    
    # Create the bar plot
    ax = sns.barplot(x="model", y="Token Count", hue="Token Type", data=df_tokens, palette="Blues")
    
    # Add labels
    plt.xlabel("Model")
    plt.ylabel("Token Count")
    plt.title("LLM Model Token Usage Comparison")
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha="right")
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "token_usage_comparison.png"), dpi=300)
    plt.close()

def plot_token_efficiency(df, output_dir=OUTPUT_DIR):
    """Plot token efficiency (tokens per second)"""
    plt.figure(figsize=(10, 6))
    
    # Sort by token efficiency
    df_sorted = df.sort_values("token_efficiency", ascending=False)
    
    # Create the bar chart
    ax = sns.barplot(x="model", y="token_efficiency", data=df_sorted, palette="Greens")
    
    # Add labels
    plt.xlabel("Model")
    plt.ylabel("Output Tokens per Second")
    plt.title("LLM Model Token Efficiency Comparison")
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha="right")
    
    # Add values on top of bars
    for i, v in enumerate(df_sorted["token_efficiency"]):
        ax.text(i, v + 0.1, f"{v:.1f}", ha="center")
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "token_efficiency_comparison.png"), dpi=300)
    plt.close()

def plot_response_similarity(df, output_dir=OUTPUT_DIR):
    """Plot average response similarity to other models"""
    plt.figure(figsize=(10, 6))
    
    # Sort by similarity
    df_sorted = df.sort_values("avg_similarity", ascending=False)
    
    # Create the bar chart
    ax = sns.barplot(x="model", y="avg_similarity", data=df_sorted, palette="Reds")
    
    # Add labels
    plt.xlabel("Model")
    plt.ylabel("Average Similarity")
    plt.title("LLM Model Response Similarity Comparison")
    plt.ylim(0, 1)  # Similarity is between 0 and 1
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha="right")
    
    # Add values on top of bars
    for i, v in enumerate(df_sorted["avg_similarity"]):
        ax.text(i, v + 0.02, f"{v:.2f}", ha="center")
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "response_similarity_comparison.png"), dpi=300)
    plt.close()

def plot_sentiment_analysis(df, output_dir=OUTPUT_DIR):
    """Plot sentiment analysis results"""
    # Check if sentiment columns exist
    if "sentiment_positive" not in df.columns:
        return
    
    plt.figure(figsize=(12, 8))
    
    # Prepare data
    sentiment_columns = ["sentiment_positive", "sentiment_negative", "sentiment_neutral"]
    
    # Create a stacked bar chart for sentiment
    df_sentiment = df[["model"] + sentiment_columns].copy()
    df_sentiment = df_sentiment.melt(
        id_vars=["model"], 
        value_vars=sentiment_columns,
        var_name="Sentiment Type", 
        value_name="Score"
    )
    
    # Clean up names for legend
    df_sentiment["Sentiment Type"] = df_sentiment["Sentiment Type"].map({
        "sentiment_positive": "Positive", 
        "sentiment_negative": "Negative",
        "sentiment_neutral": "Neutral"
    })
    
    # Create the bar plot
    ax = sns.barplot(x="model", y="Score", hue="Sentiment Type", data=df_sentiment, palette="RdYlGn")
    
    # Add labels
    plt.xlabel("Model")
    plt.ylabel("Sentiment Score")
    plt.title("LLM Model Response Sentiment Analysis")
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha="right")
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "sentiment_analysis.png"), dpi=300)
    plt.close()
    
    # Also create a plot for compound sentiment
    plt.figure(figsize=(10, 6))
    df_sorted = df.sort_values("sentiment_compound", ascending=False)
    ax = sns.barplot(x="model", y="sentiment_compound", data=df_sorted, palette="RdYlGn")
    plt.xlabel("Model")
    plt.ylabel("Compound Sentiment Score")
    plt.title("LLM Model Compound Sentiment Score (-1 to 1)")
    plt.xticks(rotation=45, ha="right")
    plt.axhline(y=0, color='gray', linestyle='-', alpha=0.3)  # Add a line at y=0
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "compound_sentiment.png"), dpi=300)
    plt.close()

def create_radar_chart(df, output_dir=OUTPUT_DIR):
    """Create a radar chart comparing models across multiple metrics"""
    # Define metrics to include
    metrics = ["token_efficiency", "avg_similarity", "avg_sentence_length"]
    
    # Check for sentiment data
    if "sentiment_positive" in df.columns:
        metrics.append("sentiment_compound")
    
    # Normalize the metrics to a 0-1 scale for fair comparison
    df_radar = df[["model"] + metrics].copy()
    
    for metric in metrics:
        if metric == "sentiment_compound":
            # Scale from [-1,1] to [0,1]
            df_radar[metric] = (df_radar[metric] + 1) / 2
        else:
            # Min-max scaling
            min_val = df_radar[metric].min()
            max_val = df_radar[metric].max()
            if max_val > min_val:
                df_radar[metric] = (df_radar[metric] - min_val) / (max_val - min_val)
    
    # Number of metrics
    N = len(metrics)
    
    # Angle of each axis
    angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
    angles += angles[:1]  # Close the loop
    
    # Labels for each axis
    labels = metrics + [metrics[0]]  # Close the loop
    
    # Convert label names to more readable format
    label_mapping = {
        "token_efficiency": "Token Efficiency",
        "avg_similarity": "Response Similarity",
        "avg_sentence_length": "Sentence Length",
        "sentiment_compound": "Positive Sentiment"
    }
    readable_labels = [label_mapping.get(l, l) for l in labels]
    
    # Create the plot
    plt.figure(figsize=(10, 8))
    ax = plt.subplot(111, polar=True)
    
    # Draw one axis per variable and add labels
    plt.xticks(angles[:-1], readable_labels[:-1], size=10)
    
    # Choose a subset of models to avoid overcrowding
    models_to_plot = df_radar["model"].tolist()
    if len(models_to_plot) > 5:
        # Pick a representative sample
        step = len(models_to_plot) // 5
        models_to_plot = models_to_plot[::step]
        if len(models_to_plot) < 5:
            models_to_plot = df_radar["model"].tolist()[:5]
    
    # Use a colormap with distinct colors
    colormap = plt.cm.get_cmap('tab10', len(models_to_plot))
    
    # Plot each model
    for i, model in enumerate(models_to_plot):
        model_data = df_radar[df_radar["model"] == model].iloc[0]
        values = [model_data[m] for m in metrics]
        values += values[:1]  # Close the loop
        
        # Plot values
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=model, color=colormap(i))
        ax.fill(angles, values, alpha=0.1, color=colormap(i))
    
    # Add legend
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # Add title
    plt.title("Model Comparison Across Multiple Metrics", size=15, y=1.1)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "model_radar_comparison.png"), dpi=300)
    plt.close()

def create_performance_summary(df, output_dir=OUTPUT_DIR):
    """Create a CSV file with model performance summary"""
    # Select and rename columns for the summary
    summary_columns = {
        "model": "Model",
        "model_api_name": "API Model Name",
        "processing_time": "Processing Time (s)",
        "tokens_total": "Total Tokens",
        "tokens_completion": "Completion Tokens",
        "token_efficiency": "Tokens/Second",
        "word_count": "Word Count",
        "sentence_count": "Sentence Count",
        "avg_sentence_length": "Avg Sentence Length",
        "avg_similarity": "Avg Similarity"
    }
    
    # Add sentiment columns if available
    if "sentiment_compound" in df.columns:
        sentiment_columns = {
            "sentiment_positive": "Positive Sentiment",
            "sentiment_negative": "Negative Sentiment",
            "sentiment_neutral": "Neutral Sentiment",
            "sentiment_compound": "Compound Sentiment"
        }
        summary_columns.update(sentiment_columns)
    
    # Create summary dataframe
    summary_df = df[list(summary_columns.keys())].copy()
    summary_df = summary_df.rename(columns=summary_columns)
    
    # Sort by processing time
    summary_df = summary_df.sort_values("Processing Time (s)")
    
    # Save to CSV
    csv_path = os.path.join(output_dir, "model_performance_summary.csv")
    summary_df.to_csv(csv_path, index=False)
    
    return summary_df, csv_path

def create_html_report(results, metrics_df, output_dir=OUTPUT_DIR):
    """Create an HTML report with all results and visualizations"""
    # Get the current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Identify the query used
    query = "Query not available"
    if results and len(results) > 0:
        # Try to reconstruct the query from the first result
        system_messages = [m for m in results[0].get("system_messages", []) if m["role"] == "user"]
        if system_messages:
            query = system_messages[0]["content"]
    
    # Create the HTML content
    html_content = f"""<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>LLM Model Comparison Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0; color: #333; }}
            .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
            h1, h2, h3 {{ color: #2c3e50; }}
            h1 {{ border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
            .header {{ background-color: #f8f9fa; padding: 20px; margin-bottom: 30px; border-radius: 5px; }}
            .charts {{ display: flex; flex-wrap: wrap; justify-content: space-between; margin-bottom: 20px; }}
            .chart {{ width: calc(50% - 20px); margin: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); border-radius: 5px; overflow: hidden; }}
            .chart img {{ width: 100%; height: auto; }}
            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
            th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
            th {{ background-color: #f2f2f2; }}
            tr:hover {{ background-color: #f5f5f5; }}
            .response {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; white-space: pre-wrap; }}
            .metrics {{ margin-bottom: 30px; }}
            .footer {{ text-align: center; margin-top: 30px; font-size: 0.9em; color: #7f8c8d; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>LLM Model Comparison Report</h1>
                <p><strong>Generated:</strong> {timestamp}</p>
            </div>

            <h2>Visualizations</h2>
            <div class="charts">
                <div class="chart">
                    <img src="processing_time_comparison.png" alt="Processing Time Comparison">
                </div>
                <div class="chart">
                    <img src="token_usage_comparison.png" alt="Token Usage Comparison">
                </div>
                <div class="chart">
                    <img src="token_efficiency_comparison.png" alt="Token Efficiency Comparison">
                </div>
                <div class="chart">
                    <img src="response_similarity_comparison.png" alt="Response Similarity">
                </div>
    """
    
    # Add sentiment charts if available
    if "sentiment_positive" in metrics_df.columns:
        html_content += """
                <div class="chart">
                    <img src="sentiment_analysis.png" alt="Sentiment Analysis">
                </div>
                <div class="chart">
                    <img src="compound_sentiment.png" alt="Compound Sentiment">
                </div>
        """
    
    # Add radar chart
    html_content += """
                <div class="chart">
                    <img src="model_radar_comparison.png" alt="Model Radar Comparison">
                </div>
            </div>
    """
    
    # Add model responses section
    html_content += """
            <h2>Model Responses</h2>
    """
    
    # Add each model's response
    for result in results:
        model_name = result["model"]
        api_name = result.get("model_api_name", "")
        
        # Check if there was an error with this model
        if "error" in result:
            html_content += f"""
            <h3>{model_name} ({api_name})</h3>
            <p class="error">Error: {result["error"]}</p>
            """
            continue
        
        # Get response details
        response_text = result["response"]
        tokens_total = result.get("tokens", {}).get("total", "N/A")
        time_seconds = result.get("time_seconds", "N/A")
        
        html_content += f"""
            <h3>{model_name} ({api_name})</h3>
            <p><strong>Processing Time:</strong> {time_seconds} seconds</p>
            <p><strong>Total Tokens:</strong> {tokens_total}</p>
            <div class="response">{response_text}</div>
        """
    
    # Close HTML
    html_content += """
            <div class="footer">
                <p>Generated by LLM Model Comparison Tool</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Write HTML to file
    html_path = os.path.join(output_dir, "model_comparison_report.html")
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    return html_path

def main():
    print("LLM Model Comparison Analysis Tool")
    print("=" * 50)
    
    # Load results
    results = load_results()
    if not results:
        print("No results found. Please run model_comparison.py first.")
        return
    
    # Calculate metrics
    print("Calculating metrics...")
    metrics_df = calculate_metrics(results)
    
    if metrics_df.empty:
        print("No valid metrics could be calculated.")
        return
    
    print(f"Analyzing results for {len(metrics_df)} models...")
    
    # Generate plots
    print("Generating visualizations...")
    plot_processing_time(metrics_df)
    plot_token_metrics(metrics_df)
    plot_token_efficiency(metrics_df)
    plot_response_similarity(metrics_df)
    plot_sentiment_analysis(metrics_df)
    create_radar_chart(metrics_df)
    
    # Create performance summary
    summary_df, csv_path = create_performance_summary(metrics_df)
    print(f"Performance summary saved to {csv_path}")
    
    # Create HTML report
    html_path = create_html_report(results, metrics_df)
    print(f"HTML report saved to {html_path}")
    
    print("\nAnalysis complete! All visualizations saved to the 'llm_outputs_images' directory.")

if __name__ == "__main__":
    main()
