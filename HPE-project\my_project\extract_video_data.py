from youtube_transcript_api import YouTubeTranscriptApi
from vector_db_utils import add_video

def process_video(video_id: str):
    """Fetches transcript and adds it to FAISS."""
    transcript_data = YouTubeTranscriptApi.get_transcript(video_id)
    transcript_text = " ".join([entry["text"] for entry in transcript_data])
    add_video(video_id, transcript_text)
    print(f"Added {video_id} to FAISS")

# Example: Process a sample video
process_video("SjOfbbfI2qY")  # Replace with any YouTube video ID
