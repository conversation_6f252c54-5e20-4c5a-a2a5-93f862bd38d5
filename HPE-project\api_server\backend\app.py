from fastapi import FastAP<PERSON>, UploadFile, File, Query
from faiss_index import load_faiss_index, process_transcripts, search_transcripts
from video_upload import save_uploaded_file
from video_upload import transcribe_youtube_video
from fastapi import FastAP<PERSON>
from video_upload import router as upload_router  # Import the router
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

app = FastAPI()

# Include the upload router
app.include_router(upload_router)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Replace * with specific frontend URL for security
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Load FAISS index
index = load_faiss_index()

from fastapi import Request

class VideoLinkRequest(BaseModel):
    video_url: str

@app.post("/video_link")
async def process_video_link(video: VideoLinkRequest):
    url = video.video_url  # Access the correct key

    if not url:
        return {"error": "Invalid URL"}

    # Step 1: Transcribe YouTube video
    transcript_path = transcribe_youtube_video(url)
    if not transcript_path:
        return {"error": "Failed to transcribe video"}

    # Step 2: Process transcripts to update FAISS index
    process_transcripts()

    return {"message": "Video processed and indexed successfully!", "transcript_path": transcript_path}
@app.options("/video_link")
async def preflight_options(request: Request):
    return {}



@app.post("/transcribe-youtube/")
async def transcribe_youtube(youtube_url: str):
    return transcribe_youtube_video(youtube_url)


@app.post("/upload/")
async def upload_video(file: UploadFile = File(...)):
    return save_uploaded_file(file)

@app.post("/rebuild-index/")
async def rebuild_index_api():
    return process_transcripts()

@app.get("/search/")
async def search_api(query: str, top_k: int = Query(5, description="Number of top results")):
    return search_transcripts(query, top_k)


import json
from models import  llm_model  # Load your LLM
import os

TRANSCRIPTS_DIR = "transcriptions/"

def load_transcript(video_title: str):
    """Load transcript JSON for a given video title."""
    transcript_path = os.path.join(TRANSCRIPTS_DIR, f"{video_title}.json")
    if not os.path.exists(transcript_path):
        return None
    with open(transcript_path, "r") as f:
        return json.load(f)

@app.get("/explain/")
async def explain_video(video_title: str, timestamp: float):
    """
    Explain or summarize the video segment at the given timestamp.
    """
    transcript = load_transcript(video_title)
    if transcript is None:
        return {"error": "Transcript not found"}

    segment_text = None
    closest_segment = None
    min_diff = float("inf")

    for segment in transcript:
        start, end, text = segment["start"], segment["end"], segment["text"]

        # If the timestamp is exactly within this segment
        if start <= timestamp <= end:
            segment_text = text
            break

        # Find the closest segment if exact match is not found
        diff = min(abs(timestamp - start), abs(timestamp - end))
        if diff < min_diff:
            min_diff = diff
            closest_segment = text

    # If no exact match was found, use the closest segment
    if not segment_text and closest_segment:
        segment_text = closest_segment

    if not segment_text:
        return {"error": "No relevant transcript found"}

    # Use LLM to summarize/explain
    prompt = f"Summarize or explain the following transcript segment:\n\n{segment_text}"
    explanation = llm_model.generate(prompt)  # Call your LLM

    return {
        "timestamp": timestamp,
        "original_text": segment_text,
        "explanation": explanation
    }