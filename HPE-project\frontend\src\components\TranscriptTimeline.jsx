import React, { useState, useEffect } from 'react';
import axios from 'axios';

const TranscriptTimeline = ({ retrievedContexts, videoId, transcriptId }) => {
  const [segments, setSegments] = useState([]);
  const [videoLength, setVideoLength] = useState(600); // Default 10 minutes in seconds
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Only proceed if we have contexts and a transcript ID
    if (!retrievedContexts || retrievedContexts.length === 0 || !transcriptId) {
      return;
    }

    const fetchTranscriptData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch the transcript data that includes timestamps
        const response = await axios.get(`http://localhost:8000/transcripts/${transcriptId}`);

        if (response.data && response.data.id) {
          // Get the full transcript with timestamps
          const fullTranscript = response.data;

          // If we have duration information, use it
          if (fullTranscript.duration) {
            setVideoLength(fullTranscript.duration);
          }

          // Now we need to match our retrieved contexts with the transcript segments
          // This is a simplified approach - in a real implementation, you'd use more
          // sophisticated text matching
          const matchedSegments = matchContextsToTranscript(retrievedContexts, fullTranscript);
          setSegments(matchedSegments);
        }
      } catch (err) {
        console.error("Error fetching transcript data:", err);
        setError("Could not load timeline data");

        // Fallback: Create mock segments based on the contexts
        const mockSegments = retrievedContexts.map((context, index) => ({
          position: (index + 1) * (100 / (retrievedContexts.length + 1)),
          text: context.substring(0, 30) + '...',
          isEstimated: true
        }));
        setSegments(mockSegments);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTranscriptData();
  }, [retrievedContexts, transcriptId]);

  // Function to match contexts to transcript segments
  const matchContextsToTranscript = (contexts, transcript) => {
    // This is a simplified matching algorithm
    // In a real implementation, you'd use more sophisticated text matching

    // Try to get the JSON transcript if available
    let jsonTranscript = [];
    try {
      // If we have a transcription property with JSON data
      if (transcript.transcription && transcript.transcription.startsWith('[')) {
        jsonTranscript = JSON.parse(transcript.transcription);
      }
    } catch (e) {
      console.error("Error parsing transcript JSON:", e);
    }

    // If we have JSON transcript with timestamps
    if (jsonTranscript.length > 0 && jsonTranscript[0].start !== undefined) {
      return matchWithJsonTranscript(contexts, jsonTranscript);
    }

    // Fallback: create estimated positions
    return contexts.map((context, index) => ({
      position: (index + 1) * (100 / (contexts.length + 1)),
      text: context.substring(0, 30) + '...',
      isEstimated: true
    }));
  };

  // Match contexts with JSON transcript that has timestamps
  const matchWithJsonTranscript = (contexts, jsonTranscript) => {
    // First, normalize the transcript segments for better matching
    const normalizedTranscript = jsonTranscript.map(segment => ({
      ...segment,
      normalizedText: segment.text.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "")
    }));

    // Calculate video duration for better positioning
    let maxTime = 0;
    for (const segment of jsonTranscript) {
      if (segment.end > maxTime) {
        maxTime = segment.end;
      }
    }

    // If we found a valid duration, update videoLength
    if (maxTime > 0 && maxTime < 7200) { // Sanity check: less than 2 hours
      setVideoLength(maxTime);
    }

    return contexts.map((context, contextIndex) => {
      // Clean and normalize the context text
      const normalizedContext = context.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "");

      // Find the best matching segment in the transcript
      let bestMatch = null;
      let bestScore = 0;

      // Try different matching strategies

      // 1. Exact phrase matching (highest priority)
      for (const segment of normalizedTranscript) {
        if (segment.normalizedText.includes(normalizedContext) ||
            normalizedContext.includes(segment.normalizedText)) {
          bestMatch = segment;
          bestScore = 1.0;
          break;
        }
      }

      // 2. Word overlap matching (if no exact match found)
      if (!bestMatch) {
        const contextWords = normalizedContext.split(/\s+/).filter(word => word.length > 3);

        for (const segment of normalizedTranscript) {
          const segmentWords = segment.normalizedText.split(/\s+/);

          // Count common words (only consider words with length > 3 to avoid common words)
          let commonWords = 0;
          let importantWordMatches = 0;

          for (const word of contextWords) {
            if (segmentWords.includes(word)) {
              commonWords++;
              if (word.length > 5) { // Important words are longer
                importantWordMatches++;
              }
            }
          }

          // Calculate score with bonus for important word matches
          const score = (commonWords / Math.max(contextWords.length, 1)) +
                        (importantWordMatches * 0.2);

          if (score > bestScore) {
            bestScore = score;
            bestMatch = segment;
          }
        }
      }

      if (bestMatch && bestScore > 0.25) { // Lower threshold for a match
        // Calculate position as percentage of video length
        const position = (bestMatch.start / maxTime) * 100;
        return {
          position: Math.min(position, 98), // Cap at 98% to ensure visibility
          text: context.substring(0, 30) + '...',
          time: bestMatch.start,
          isEstimated: false,
          score: bestScore
        };
      }

      // Fallback for no good match - distribute evenly
      return {
        position: 5 + (contextIndex * (90 / Math.max(contexts.length, 1))),
        text: context.substring(0, 30) + '...',
        isEstimated: true
      };
    });
  };

  // Format time in MM:SS format
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return <div className="text-center py-2 text-gray-500">Loading timeline...</div>;
  }

  if (error && segments.length === 0) {
    return <div className="text-center py-2 text-red-500">{error}</div>;
  }

  if (segments.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 mb-1">
      <div className="flex items-center justify-between mb-1">
        <h3 className="text-xs font-medium text-gray-600">Information Timeline</h3>
        <div className="flex items-center text-xs text-gray-500">
          <span className="flex items-center mr-2">
            <span className="inline-block w-2 h-2 bg-indigo-500 mr-1 rounded-full"></span>
            <span className="text-xs">Matched</span>
          </span>
          <span className="flex items-center">
            <span className="inline-block w-2 h-2 bg-amber-400 mr-1 rounded-full"></span>
            <span className="text-xs">Estimated</span>
          </span>
        </div>
      </div>

      <div className="relative h-7 bg-gray-100 rounded-md overflow-hidden border border-gray-200 shadow-inner">
        {/* Timeline gradient background */}
        <div className="absolute top-0 left-0 h-full w-full bg-gradient-to-r from-gray-50 to-gray-100"></div>

        {/* Segment markers */}
        {segments.map((segment, index) => {
          // Ensure segments are distributed more evenly if they're estimated
          const position = segment.isEstimated
            ? Math.min(5 + (index * (90 / Math.max(segments.length, 1))), 95)
            : segment.position;

          return (
            <div
              key={index}
              className={`absolute top-0 h-full w-0.5 ${
                segment.isEstimated
                  ? 'bg-amber-400'
                  : 'bg-indigo-500'
              } hover:w-1.5 transition-all duration-200 cursor-pointer group`}
              style={{
                left: `${position}%`,
                boxShadow: segment.isEstimated ? 'none' : '0 0 3px rgba(79, 70, 229, 0.5)'
              }}
              title={`${segment.text}${segment.time ? ` (${formatTime(segment.time)})` : ''}`}
            >
              <div className={`
                absolute -top-1 left-0 transform -translate-x-1/2
                ${segment.isEstimated ? 'bg-amber-400' : 'bg-indigo-500'}
                text-white text-xs w-4 h-4 flex items-center justify-center rounded-full
                shadow-sm group-hover:scale-110 transition-transform duration-200
              `}>
                {index + 1}
              </div>

              {/* Tooltip on hover */}
              <div className="absolute -top-8 left-0 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                {segment.text.substring(0, 40)}{segment.text.length > 40 ? '...' : ''}
                {segment.time ? ` (${formatTime(segment.time)})` : ''}
              </div>
            </div>
          );
        })}

        {/* Time markers with tick marks */}
        <div className="absolute bottom-0 w-full h-2 flex items-end">
          {[0, 20, 40, 60, 80, 100].map(percent => (
            <div
              key={percent}
              className="absolute bottom-0 flex flex-col items-center"
              style={{ left: `${percent}%` }}
            >
              <div className="h-2 w-0.5 bg-gray-300"></div>
              <div className="text-[9px] text-gray-500 mt-0.5">
                {formatTime((percent / 100) * videoLength)}
              </div>
            </div>
          ))}

          {/* Subtle tick marks between main markers */}
          {[10, 30, 50, 70, 90].map(percent => (
            <div
              key={percent}
              className="absolute bottom-0 flex flex-col items-center"
              style={{ left: `${percent}%` }}
            >
              <div className="h-1 w-0.5 bg-gray-200"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TranscriptTimeline;
