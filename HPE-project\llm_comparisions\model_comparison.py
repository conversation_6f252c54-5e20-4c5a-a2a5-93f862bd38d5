import os
import json
import time
import numpy as np
from groq import Groq
import argparse
from datetime import datetime

# Ensure output directory exists
os.makedirs("llm_outputs_images", exist_ok=True)

# Parse command line arguments
parser = argparse.ArgumentParser(description="Compare different LLM models using Groq API")
parser.add_argument("--query", type=str, default="Explain quantum computing in simple terms.", 
                    help="The query to test across all models")
parser.add_argument("--vector_bin", type=str, default="temp.bin", 
                    help="Path to the binary file containing vector embeddings")
parser.add_argument("--buffer", type=int, default=15, 
                    help="Buffer time in seconds between API calls to avoid rate limits")
parser.add_argument("--api_key", type=str, 
                    help="Groq API key. If not provided, will look for GROQ_API_KEY environment variable")
args = parser.parse_args()

# Get API key
api_key = args.api_key or os.environ.get("GROQ_API_KEY")
if not api_key:
    api_key = "********************************************************"  # Using the API key from faiss_backend.py

# Initialize Groq client
client = Groq(api_key=api_key)

# Model mapping (frontend names to Groq API names)
MODEL_MAPPING = {
    "llama-3.3-70b-versatile": "llama3-70b-8192",
    "llama-3.1-8b-instant": "llama3-8b-8192",
    "gemma2-9b-it": "gemma2-9b-it",
    "meta-llama/llama-4-maverick-17b-128e-instruct": "llama-4-maverick-17b-128e-instruct",
    "deepseek-r1-distill-llama-70b": "deepseek-r1-distill-llama-70b",
    "qwen-qwq-32b": "qwen-qwq-32b",
    "compound-beta / mini": "compound-beta-mini"
}

# List of all models to test
MODELS = list(MODEL_MAPPING.keys())

def load_vector_context(vector_bin_path):
    """
    Load vector embeddings from a binary file to create context for queries.
    This is optional and can be modified based on your specific use case.
    """
    try:
        with open(vector_bin_path, "rb") as f:
            vector_data = f.read()
        return f"Using context from {vector_bin_path} with {len(vector_data)} bytes of vector data."
    except Exception as e:
        print(f"Warning: Could not load vector data from {vector_bin_path}: {e}")
        return ""

def generate_response(model_name, query, system_prompt=None):
    """
    Generate a response from a specified model using the Groq API
    """
    groq_model_name = MODEL_MAPPING.get(model_name)
    if not groq_model_name:
        return {"error": f"Model {model_name} not found in mapping"}
    
    print(f"Generating response from {model_name} ({groq_model_name})...")
    
    try:
        # Default system prompt if none provided
        if system_prompt is None:
            system_prompt = "You are a helpful, concise assistant. Provide clear, factual information."
        
        start_time = time.time()
        
        response = client.chat.completions.create(
            model=groq_model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ],
            temperature=0.3,
            max_tokens=500
        )
        
        end_time = time.time()
        
        response_text = response.choices[0].message.content
        tokens_used = response.usage.total_tokens
        completion_tokens = response.usage.completion_tokens
        prompt_tokens = response.usage.prompt_tokens
        
        result = {
            "model": model_name,
            "model_api_name": groq_model_name,
            "response": response_text,
            "tokens": {
                "total": tokens_used,
                "completion": completion_tokens,
                "prompt": prompt_tokens
            },
            "time_seconds": round(end_time - start_time, 3),
            "timestamp": datetime.now().isoformat()
        }
        
        return result
    
    except Exception as e:
        print(f"Error with model {model_name}: {e}")
        return {
            "model": model_name,
            "model_api_name": groq_model_name,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def main():
    # Load vector context (optional)
    vector_context = load_vector_context(args.vector_bin)
    
    # Prepare the query with optional context
    query = args.query
    if vector_context:
        query = f"{vector_context}\n\nWith this context in mind, please answer: {query}"
    
    # System prompt
    system_prompt = (
        "You are a helpful, concise assistant that excels at providing accurate information. "
        "Base your response solely on factual knowledge. Be clear and succinct."
    )
    
    # Test all models and collect results
    results = []
    for model_name in MODELS:
        try:
            result = generate_response(model_name, query, system_prompt)
            results.append(result)
            
            # Save intermediate results after each model
            with open("llm_outputs.json", "w") as f:
                json.dump(results, f, indent=2)
            
            print(f"Completed {model_name}. Waiting {args.buffer} seconds before next call...")
            time.sleep(args.buffer)  # Buffer between API calls to avoid rate limits
            
        except Exception as e:
            print(f"Error processing model {model_name}: {e}")
            # Add error entry to results
            results.append({
                "model": model_name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    # Save final results
    with open("llm_outputs.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"All model outputs saved to llm_outputs.json")
    print(f"Run the analysis script to generate visualizations.")

if __name__ == "__main__":
    main()
