# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript and enabling type-aware lint rules. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.

---

## 🚀 Running with Docker

### **Prerequisites**
- Install Docker: [Docker Installation Guide](https://docs.docker.com/get-docker/)

### **Building and Running the Docker Container**

#### **Build the Docker Image**
```sh
docker build -t my-frontend .
```

#### **Run the Docker Container**
```sh
docker run -d -p 3000:80 my-frontend
```
- `-d` runs the container in detached mode
- `-p 3000:80` maps the container port to your local machine

#### **Check Running Containers**
```sh
docker ps
```

### **Stopping & Removing Containers**

#### **Stop the Container**
```sh
docker stop <container_id>
```

#### **Remove the Container**
```sh
docker rm <container_id>
```

This ensures that your React + Vite project runs smoothly using Docker. 🚀

