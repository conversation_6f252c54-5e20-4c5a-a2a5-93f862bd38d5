from flask import Flask, request, jsonify, Response, stream_with_context
from flask_cors import CORS
from faster_whisper import WhisperModel
import os
import uuid
import json
import tempfile

app = Flask(__name__)
CORS(app, origins=["http://localhost:3000"])
TEMP_DIR = tempfile.gettempdir()

@app.route('/upload', methods=['POST'])
def upload_video():
    if 'video' not in request.files:
        return jsonify({'error': 'No video file provided'}), 400

    video_file = request.files['video']

    filename = f"{uuid.uuid4()}_{video_file.filename}"
    temp_path = os.path.join(TEMP_DIR, filename)

    video_file.save(temp_path)

    transcription = transcribe_video(temp_path)
    
    def generate_transcription():
        model = WhisperModel("small", device="cuda", compute_type="float16")
        segments, info = model.transcribe(temp_path)
        
        for segment in segments:
            data = json.dumps({'segment': segment.text})
            yield f"data: {data}\n\n"
        
        yield f"data: {json.dumps({'complete': True})}\n\n"
        
        os.remove(temp_path)
    
    return Response(stream_with_context(generate_transcription()), 
                   mimetype='text/event-stream')


def transcribe_video(filepath):
    model = WhisperModel("small", device="cuda", compute_type="float16")
    segments, info = model.transcribe(filepath)

    transcribed = ""

    transcribed_text = "".join(segment.text for segment in segments)

    return transcribed_text
    
if __name__ == '__main__':
    app.run(debug=True)
