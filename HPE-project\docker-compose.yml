#version: "3.9"

services:
  frontend:
    build: ./frontend
    container_name: frontend
    ports:
      - "3001:80"
    depends_on:
      - server
      - faiss_backend
    networks:
      - app-network

  server:
    build:
      context: ./api_server
      dockerfile: Dockerfile.server
    container_name: server
    ports:
      - "8000:8000"
    volumes:
      - ./chat_mappings:/app/chat_mappings
      - ./uploads:/app/uploads
      - ./transcripts:/app/transcripts
      - ./audio_output:/app/audio_output
      - ./videos:/app/videos
      - ./embeddings:/app/embeddings 
      - ./merged_embeddings:/app/merged_embeddings
    environment:
      - BACKEND_EMBED_URL=http://faiss_backend:8001/embed
      - external_service_url=http://faiss_backend:8001/chat
      
    networks:
      - app-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  faiss_backend:
    build:
      context: ./api_server
      dockerfile: Dockerfile.faiss
    container_name: faiss_backend
    ports:
      - "8001:8001"
    volumes:
      - ./chat_mappings:/app/chat_mappings
      - ./faiss_data:/app/faiss_data
      - ./transcripts:/app/transcripts
      - ./embeddings:/app/embeddings
      - ./videos:/app/videos
      - ./merged_embeddings:/app/merged_embeddings
    environment:
      - OLLAMA_HOST=http://host.docker.internal:11434
      - GROQ_API_KEY=********************************************************
    networks:
      - app-network

networks:
  app-network:
    driver: bridge


