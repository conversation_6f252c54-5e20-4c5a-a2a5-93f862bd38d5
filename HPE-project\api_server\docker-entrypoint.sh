#!/bin/bash
set -e

# Update the BACKEND_EMBED_URL from environment variable if set
if [ -n "$BACKEND_EMBED_URL" ]; then
    echo "Setting BACKEND_EMBED_URL to $BACKEND_EMBED_URL"
    sed -i "s|BACKEND_EMBED_URL = \"http://127.0.0.1:8001/embed\"|BACKEND_EMBED_URL = \"$BACKEND_EMBED_URL\"|g" server.py
fi

# Update the external_service_url default in ChatRequest class
if [ -n "$CHAT_SERVICE_URL" ]; then
    echo "Setting default external_service_url to $CHAT_SERVICE_URL"
    sed -i "s|external_service_url: str = \"http://localhost:8001/chat\"|external_service_url: str = \"$CHAT_SERVICE_URL\"|g" server.py
fi

# Execute the command passed to the script
exec "$@"
