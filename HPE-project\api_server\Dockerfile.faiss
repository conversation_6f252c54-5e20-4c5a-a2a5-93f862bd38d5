FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip
RUN pip3 install --no-cache-dir --upgrade pip

# Copy only requirements first to leverage Docker cache
COPY faiss_backend_requirements.txt ./

# Install dependencies directly from requirements
RUN pip3 install --no-cache-dir -r faiss_backend_requirements.txt

# Copy the rest of the application
COPY . .

# Create a script to modify the faiss_backend.py file
RUN echo '#!/bin/bash\n\
# Force model to use CPU\n\
sed -i "s/model = SentenceTransformer(\"all-MiniLM-L6-v2\")/model = SentenceTransformer(\"all-MiniLM-L6-v2\").to(\"cpu\")/" faiss_backend.py\n\
# Set Groq API key if provided\n\
if [ -n "$GROQ_API_KEY" ]; then\n\
    sed -i "s/client = Groq(api_key=\"********************************************************\")/client = Groq(api_key=\"$GROQ_API_KEY\")/" faiss_backend.py\n\
fi\n\
exec "$@"' > /app/entrypoint.sh && \
chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["uvicorn", "faiss_backend:app", "--host", "0.0.0.0", "--port", "8001"]








