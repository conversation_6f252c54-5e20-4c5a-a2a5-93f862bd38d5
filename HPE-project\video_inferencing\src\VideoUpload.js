import React, { useState } from 'react';
import './VideoUpload.css';
import axios from 'axios';
import DropZone from './components/Dropzone';

function VideoUpload() {
  const [transcription, setTranscription] = useState('');

  const handleChange = async (file) => {
    console.log(file)
    const formData = new FormData();
    formData.append('file', file);

    setTranscription('');

    try {
      // gpt
      // Make a POST request to start the transcription
      const response = await axios.post('http://localhost:8000/transcribe', formData);

      
      setTranscription(response.data.transcription);
      console.log(response)

    } catch (e) {
      console.log('Upload or transcription failed: ', e);
    }
  }

  return (
    <div
      id="div-file-upload"
    >
      <DropZone
        onFileChange={(file) => handleChange(file)}
      />
      <div className="transcription-container">
        {transcription}
      </div>
    </div>
  );
}

export default VideoUpload;
