import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { ChevronDown, Check } from 'lucide-react';

const Modal = ({ isOpen, onClose, onSubmit, title }) => {
  const [chatName, setChatName] = useState('');
  const [selectedVideos, setSelectedVideos] = useState([]);
  const [savedEmbeddings, setSavedEmbeddings] = useState([]);
  const [isLoadingEmbeddings, setIsLoadingEmbeddings] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);

  // Fetch saved embeddings when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchSavedEmbeddings();
    }
  }, [isOpen]);

  // Reset state when modal closes
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
    // Reset inputs when modal closes
    if (!isOpen) {
      setChatName('');
      setSelectedVideos([]);
    }
  }, [isOpen]);

  // Handle clicks outside the dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch available embeddings
  const fetchSavedEmbeddings = async () => {
    try {
      setIsLoadingEmbeddings(true);
      const response = await axios.get('http://localhost:8000/video_embeddings');
      setSavedEmbeddings(response.data.embeddings || []);
    } catch (error) {
      console.error('Error fetching video embeddings:', error);
      setSavedEmbeddings([]);
    } finally {
      setIsLoadingEmbeddings(false);
    }
  };

  // Toggle video selection
  const toggleVideoSelection = (embedding) => {
    setSelectedVideos(prev => {
      if (prev.includes(embedding)) {
        return prev.filter(v => v !== embedding);
      } else {
        return [...prev, embedding];
      }
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (chatName.trim() && selectedVideos.length > 0) {
      onSubmit({
        chatName: chatName.trim(),
        selectedVideos
      });
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md mx-4">
        <h2 className="text-xl font-bold mb-4">{title || 'Create New Chat'}</h2>
        <form onSubmit={handleSubmit}>
          {/* Chat Name Input */}
          <div className="mb-4">
            <label htmlFor="chatName" className="block text-sm font-medium text-gray-700 mb-1">
              Chat Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="chatName"
              ref={inputRef}
              value={chatName}
              onChange={(e) => setChatName(e.target.value)}
              placeholder="Enter a name for this chat"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              Give your chat a descriptive name
            </p>
          </div>

          {/* Video Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Videos <span className="text-red-500">*</span>
            </label>
            <div className="border border-gray-300 rounded-md max-h-60 overflow-y-auto">
              {isLoadingEmbeddings ? (
                <div className="p-4 text-sm text-gray-500 flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Loading videos...
                </div>
              ) : savedEmbeddings.length > 0 ? (
                <ul className="divide-y divide-gray-200">
                  {savedEmbeddings.map((embedding) => (
                    <li
                      key={embedding}
                      onClick={() => toggleVideoSelection(embedding)}
                      className={`px-4 py-3 text-sm cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                        selectedVideos.includes(embedding) ? 'bg-indigo-50' : ''
                      }`}
                    >
                      <span className="truncate">{embedding}</span>
                      {selectedVideos.includes(embedding) && (
                        <Check className="h-4 w-4 text-indigo-600" />
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="p-4 text-sm text-gray-500 text-center">
                  No videos available. Upload videos first.
                </div>
              )}
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Select one or more videos to include in this chat
            </p>
            {selectedVideos.length > 0 && (
              <div className="mt-2 text-xs text-indigo-600">
                {selectedVideos.length} video{selectedVideos.length !== 1 ? 's' : ''} selected
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!chatName.trim() || selectedVideos.length === 0}
              className={`px-4 py-2 text-sm font-medium text-white rounded-md transition-colors ${
                chatName.trim() && selectedVideos.length > 0
                  ? 'bg-indigo-600 hover:bg-indigo-700'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              Create Chat
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Modal;
