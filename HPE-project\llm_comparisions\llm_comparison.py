"""
LLM Model Comparison Script

This script compares different LLM models (Ollama's TinyLlama and Groq's advanced models)
on the same query using vector embeddings from temp.bin.
"""

import os
import time
import json
import numpy as np
import ollama
from groq import Groq
from groq.types.chat import ChatCompletion
import concurrent.futures


# Constants
QUERY = "What is an ebpf map"  # Edit this query as needed
EMBEDDING_FILE = "temp.bin"  # Path to vector embedding
RESULTS_FILE = "comparison_results.json"

# API configuration
# Note: You need a valid Groq API key - the current one seems invalid
GROQ_API_KEY = "********************************************************"  # Replace with your valid API key

# Enable Ollama for tinyllama model comparison
USE_OLLAMA = True

# List of models to compare
MODELS = {}

# Add tinyllama from Ollama (as used in faiss_backend.py)
if USE_OLLAMA:
    MODELS["tinyllama"] = {
        "name": "tinyllama",
        "type": "ollama"
    }

# Add Groq model for llama3-70b
if GROQ_API_KEY:
    MODELS["llama3-70b"] = {
        "name": "llama3-70b-8192",
        "type": "groq"
    }
    # Removed deepseek model as requested

def load_embeddings(file_path):
    """Load embeddings from binary file"""
    try:
        with open(file_path, "rb") as f:
            # Assuming float32 embeddings
            binary_data = f.read()
            # Convert to numpy array assuming float32 type
            # We won't reshape it since we're not using the embeddings for similarity search
            embeddings = np.frombuffer(binary_data, dtype=np.float32)
        return embeddings
    except Exception as e:
        print(f"Error loading embeddings: {str(e)}")
        return None

def query_ollama(model_name, query):
    """Query Ollama model with the given prompt (matching setup from faiss_backend.py)"""
    start_time = time.time()
    try:
        # Use the same system prompt as in faiss_backend.py
        response = ollama.chat(model=model_name, messages=[
            {
                'role': 'system', 
                'content': 'You are a precise AI assistant. Extract and summarize key information directly. Be concise and factual. Keep the length of the response short.'
            },
            {
                'role': 'user', 
                'content': query
            }
        ])
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        return {
            "response": response['message']['content'].strip(),
            "execution_time": execution_time,
            "token_count": len(response.get('message', {}).get('content', '').split()),
            "model": model_name,
            "success": True
        }
    except Exception as e:
        end_time = time.time()
        return {
            "response": f"Error: {str(e)}",
            "execution_time": end_time - start_time,
            "token_count": 0,
            "model": model_name,
            "success": False
        }

def query_groq(model_name, query):
    """Query Groq API with the given prompt using the official Groq client"""
    start_time = time.time()
    
    if not GROQ_API_KEY:
        return {
            "response": "Error: GROQ_API_KEY not set. Please set it in your .env file or environment variables.",
            "execution_time": 0,
            "token_count": 0,
            "model": model_name,
            "success": False
        }
    
    try:
        # Initialize the Groq client
        client = Groq(api_key=GROQ_API_KEY)
        
        # Create the chat completion
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "You are a helpful AI assistant. Provide a clear, accurate response."},
                {"role": "user", "content": query}
            ],
            temperature=0.7,
            max_tokens=1000
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        return {
            "response": response.choices[0].message.content.strip(),
            "execution_time": execution_time,
            "token_count": response.usage.completion_tokens if hasattr(response, 'usage') else 0,
            "model": model_name,
            "success": True
        }
    except Exception as e:
        end_time = time.time()
        return {
            "response": f"Error: {str(e)}",
            "execution_time": end_time - start_time,
            "token_count": 0,
            "model": model_name,
            "success": False
        }

def run_comparison(query, models=MODELS):
    """Compare different LLM models on the same query"""
    results = []
    
    print(f"Running comparison for query: {query}")
    
    for model_id, model_info in models.items():
        print(f"Processing model: {model_info['name']}")
        
        if model_info['type'] == 'ollama':
            result = query_ollama(model_info['name'], query)
        elif model_info['type'] == 'groq':
            result = query_groq(model_info['name'], query)
        else:
            print(f"Unknown model type: {model_info['type']}")
            continue
        
        result['model_id'] = model_id
        results.append(result)
        
        status = "Success" if result['success'] else "Failed"
        print(f"Completed: {model_info['name']} - {status} (Time: {result['execution_time']:.2f}s)")
    
    return results

def save_results(results, query, filename=RESULTS_FILE):
    """Save comparison results to a JSON file"""
    data = {
        "query": query,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "results": results
    }
    
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"Results saved to {filename}")

if __name__ == "__main__":
    print("LLM Model Comparison Tool")
    print("=" * 50)
    
    # Load embeddings (not used for similarity search in this simplified version)
    embeddings = load_embeddings(EMBEDDING_FILE)
    if embeddings is not None:
        print(f"Loaded embeddings from {EMBEDDING_FILE}")
    
    # Run the comparison
    results = run_comparison(QUERY)
    
    # Save results
    save_results(results, QUERY)
    
    print("\nComparison Results:")
    print("-" * 50)
    for result in results:
        success_indicator = "SUCCESS" if result["success"] else "FAILED"
        print(f"{success_indicator}: {result['model']} - Time: {result['execution_time']:.2f}s")
    
    print("\nDetailed responses saved to comparison_results.json")
    print("Run the analysis script to generate comparison charts.")
