import json
import matplotlib.pyplot as plt

def load_benchmark_data(filepath='whisper_benchmarks.json'):
    """Load benchmark data from JSON file."""
    with open(filepath, 'r') as f:
        return json.load(f)

def create_benchmark_visualizations(data):
    """Create multiple separate visualizations of benchmark results."""
    # Prepare data
    model_results = data['model_results']
    models = list(model_results.keys())
    
    # Color palette
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    # 1. Load Time Comparison
    plt.figure(figsize=(10, 6))
    plt.bar(models, [model_results[model]['load_time_seconds'] for model in models], color=colors)
    plt.title('Whisper Model Load Time', fontsize=16)
    plt.ylabel('Seconds')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('whisper_load_time.png', dpi=300)
    plt.close()
    
    # 2. Transcription Time Comparison
    plt.figure(figsize=(10, 6))
    plt.bar(models, [model_results[model]['transcription_time_seconds'] for model in models], color=colors)
    plt.title('Whisper Model Transcription Time', fontsize=16)
    plt.ylabel('Seconds')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('whisper_transcription_time.png', dpi=300)
    plt.close()
    
    # 3. Memory Peak Comparison
    plt.figure(figsize=(10, 6))
    plt.bar(models, [model_results[model]['memory_peak_mb'] for model in models], color=colors)
    plt.title('Whisper Model Peak Memory Usage', fontsize=16)
    plt.ylabel('Memory (MB)')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('whisper_memory_usage.png', dpi=300)
    plt.close()
    
    # 4. Transcription Speed (Real-Time Factor) Comparison
    plt.figure(figsize=(10, 6))
    plt.bar(models, [model_results[model]['transcription_speed_rtf'] for model in models], color=colors)
    plt.title('Whisper Model Transcription Speed', fontsize=16)
    plt.ylabel('Real-Time Factor (RTF)')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('whisper_transcription_speed.png', dpi=300)
    plt.close()

    print("Benchmark visualizations saved:")
    print("- whisper_load_time.png")
    print("- whisper_transcription_time.png")
    print("- whisper_memory_usage.png")
    print("- whisper_transcription_speed.png")

def main():
    # Load benchmark data
    benchmark_data = load_benchmark_data()
    
    # Create visualizations
    create_benchmark_visualizations(benchmark_data)

if __name__ == "__main__":
    main()