# from fastapi import HTTPException
# import whisper_s2t
# import torch
# import time
# import gc
# import os
# import json
# from datetime import datetime
# import pytube
# import re


# link = "https://www.youtube.com/watch?v=6YVvGwOeuMU"

# def extract_youtube_id(url):
#     patterns = [
#         r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
#         r'youtube\.com\/watch\?v=([^&]+)',
#         r'youtu\.be\/([^?]+)'
#     ]
    
#     for pattern in patterns:
#         match = re.search(pattern, url)
#         if match:
#             return match.group(1)
    
#     return None

# from pytube import YouTube

# def get_video_title(url):
#     try:
#         yt = YouTube(url)
#         return yt.title
#     except KeyError:
#         print("Warning: 'videoDetails' key not found in YouTube metadata.")
#         return "Unknown Title"
#     except Exception as e:
#         print(f"Error fetching video title: {str(e)}")
#         return "Unknown Title"

# def get_video_title(url):
#     try:
#         yt = YouTube(url)
#         return yt.title
#     except Exception:
#         import requests
#         response = requests.get(url)
#         match = re.search(r'<title>(.*?)</title>', response.text)
#         if match:
#             return match.group(1).replace(" - YouTube", "").strip()
#         return "Unknown Title"


# def process_link(link):
#     url = link
    
#     timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    
#     try:
#         video_id = extract_youtube_id(url)
        
#         print(video_id)
#         if video_id:
#             try:
#                 yt = pytube.YouTube(url)
#                 video_title = get_video_title(yt.title)
#                 print(video_title)
#                 safe_title = re.sub(r'[^\w\-_\. ]', '_', video_title)
#                 unique_id = f"{timestamp}_youtube_{video_id}"
                
#                 audio_stream = yt.streams.filter(only_audio=True).first()
                
#                 if not audio_stream:
#                     raise HTTPException(status_code=400, detail="No audio stream found for the video")

#                 temp_dir = "temp_downloads"
#                 os.makedirs(temp_dir, exist_ok=True)
                
#                 print(f"Downloading audio from YouTube video: {video_title}")
#                 temp_file_path = audio_stream.download(output_path=temp_dir)
#                 temp_file_name = os.path.basename(temp_file_path)
                
#                 start = time.time()
                
#                 files = [temp_file_path]
#                 lang_codes = ["en"]
#                 tasks = ["transcribe"]
#                 initial_prompts = [None]
                
#                 out = model.transcribe_with_vad(files, lang_codes=lang_codes, tasks=tasks, initial_prompts=initial_prompts, batch_size=batch_size)
                
#                 transcript_dir = os.path.join(transcripts_dir, unique_id)
#                 os.makedirs(transcript_dir, exist_ok=True)
                
#                 vtt_path = os.path.join(transcript_dir, "transcript.vtt")
#                 whisper_s2t.write_outputs(out, format='vtt', op_files=[vtt_path])
                
#                 json_path = os.path.join(transcript_dir, "transcript.json")
#                 whisper_s2t.write_outputs(out, format='json', op_files=[json_path])
                
#                 with open(json_path, "r") as f:
#                     data = json.load(f)
                
#                 cleaned_data = clean_transcription_data(data)
#                 with open(json_path, "w") as f:
#                     json.dump(cleaned_data, f, indent=4)
                
#                 with open(vtt_path, "r") as f:
#                     vtt_content = f.read()
#                 transcript_text = vtt_content[6:] if vtt_content.startswith("WEBVTT") else vtt_content
                
#                 txt_path = os.path.join(transcript_dir, "transcript.txt")
#                 with open(txt_path, "w") as f:
#                     f.write(transcript_text)
                
#                 os.remove(temp_file_path)
                
#                 end = time.time()
#                 processing_time = end - start
#                 print(f"\nTime taken for transcription: {processing_time}")
                
#                 transcript_meta = {
#                     "id": unique_id,
#                     "filename": f"YouTube: {video_title}",
#                     "url": url,
#                     "video_id": video_id,
#                     "timestamp": datetime.now().isoformat(),
#                     "duration": processing_time,
#                     "length": len(transcript_text)
#                 }
                
#                 transcripts_index.append(transcript_meta)
#                 save_transcripts_index()
                
#                 gc.collect()
#                 torch.cuda.empty_cache()
                
#                 return {
#                     "id": unique_id,
#                     "transcription": transcript_text,
#                     "filename": f"YouTube: {video_title}",
#                     "video_id": video_id,
#                     "url": url,
#                     "timestamp": transcript_meta["timestamp"]
#                 }
                
#             except Exception as e:
#                 raise HTTPException(status_code=500, detail=f"YouTube processing error: {str(e)}")
#         else:
#             unique_id = f"{timestamp}_link_{url.replace('://', '_').replace('/', '_').replace('.', '_')}"
#             raise HTTPException(status_code=400, detail="Only YouTube links are supported at this time")
            
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error processing link: {str(e)}")


# process_link(link)

import pytubefix 

link = "https://www.youtube.com/watch?v=6YVvGwOeuMU"

yt = pytubefix.YouTube(link)

print(yt.captions['a.en'].generate_srt_captions())
