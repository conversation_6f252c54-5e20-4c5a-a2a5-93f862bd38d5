import os
from TTS.api import TTS

model_id = "tts_models/en/ljspeech/vits--neon"  # <-- note the double hyphen!

text = "As an AI assistant, I was fascinated by <PERSON><PERSON><PERSON>'s work in physics and found his lectures obliterated from history. After discovering them, I began researching and discovered that he gave the BBC Wreaht lecture series in 1953. <PERSON><PERSON><PERSON>'s and <PERSON><PERSON><PERSON>'s contributions were equally impressive, leading to <PERSON><PERSON><PERSON>'s reputation as a masterful speaker in his field. While <PERSON><PERSON><PERSON> was a hero to my heroes of physics, his contribution to the field is still largely unrecognized."
output_dir = "debug_outputs"
os.makedirs(output_dir, exist_ok=True)
output_path = os.path.join(output_dir, "neon_vits_debug.wav")

try:
    print(f"🔄 Loading model: {model_id}")
    tts = TTS(model_id).to("cuda")  # or "cpu" if CUDA is unavailable

    print(f"📢 Synthesizing: \"{text}\"")
    tts.tts_to_file(text=text, file_path=output_path)

    print(f"✅ Audio saved to: {output_path}")

except Exception as e:
    print(f"❌ Error occurred: {e}")
