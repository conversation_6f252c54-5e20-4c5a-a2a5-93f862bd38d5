I'll help you dockerize this FastAPI server. Let's create a Docker configuration for your speech-to-text transcription service.

First, here's a Dockerfile that you can place in your backend folder:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies required for PyTorch and audio processing
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install PyTorch and CUDA dependencies
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy the rest of the application
COPY . .

# Create directory for transcripts
RUN mkdir -p transcripts

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

You'll need to create a requirements.txt file in your backend folder with the following dependencies:

```
fastapi
uvicorn
pydantic
python-multipart
whisper-s2t
pytubefix
```

Now, let's create a docker-compose.yml file that will help you run the container and mount your local directories:

```yaml
version: '3'

services:
  transcription-api:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/transcripts:/app/transcripts
    environment:
      - PYTHONUNBUFFERED=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

To build and run your Docker container:

1. Make sure you have Docker and Docker Compose installed
2. Put the Dockerfile in your backend folder
3. Create the requirements.txt file in the backend folder
4. Put the docker-compose.yml file in the parent directory of your backend folder
5. Run these commands:

```bash
# Build the Docker image
docker-compose build

# Run the container
docker-compose up
```

Important notes:

1. The Dockerfile includes CUDA support since your code uses CUDA for GPU acceleration. If you're not using a GPU, you can modify the Dockerfile to use CPU only.

2. The docker-compose.yml file includes NVIDIA GPU passthrough. This requires:
   - Docker version 19.03 or newer
   - NVIDIA Container Toolkit installed
   - A compatible NVIDIA GPU

3. If you're not using a GPU, remove the `deploy` section from the docker-compose.yml file.

4. The volumes in docker-compose.yml mount your local backend folder to the container, so any changes you make will be reflected immediately. The transcripts folder is also mounted so your transcription results will persist.

Let me know if you need any clarification or have any questions about this setup!


Let's create a Docker setup for your React frontend built with Vite. Here's a comprehensive guide including the Dockerfile and other configuration details.

First, let's create a Dockerfile for your frontend. Create a file named `Dockerfile` (no extension) in your frontend directory:

```dockerfile
# Build stage
FROM node:20-alpine AS build

WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json ./
RUN npm ci

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy the built files from the build stage to nginx server
COPY --from=build /app/dist /usr/share/nginx/html

# Copy a custom nginx configuration if needed
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

Next, create an `nginx.conf` file in your frontend directory:

```
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to the backend
    location /api/ {
        proxy_pass http://transcription-api:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Now, update your `docker-compose.yml` to include both the frontend and backend:

```yaml
version: '3'

services:
  transcription-api:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/transcripts:/app/transcripts
    environment:
      - PYTHONUNBUFFERED=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - transcription-api
```

You might need to update your Vite configuration to work properly with the API during development. Update your `vite.config.js`:

```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

Now, for the communication between frontend and backend, update your API calls in the frontend code to use a consistent base URL. Create a `src/config.js` file:

```javascript
const API_BASE_URL = import.meta.env.PROD 
  ? '/api' 
  : 'http://localhost:8000';

export { API_BASE_URL };
```

Then use this in your API calls:

```javascript
// Example in your frontend components
import { API_BASE_URL } from '../config';
import axios from 'axios';

// Use it in API calls
axios.post(`${API_BASE_URL}/transcribe/`, formData);
```

To build and run everything:

```bash
# Navigate to your project root (where docker-compose.yml is)
cd your-project-root

# Build the containers
docker-compose build

# Run the services
docker-compose up
```

Additional tips for Windows with Docker Desktop:

1. Make sure Docker Desktop is running before executing any Docker commands.

2. If you have issues with line endings, create a `.dockerignore` file in both frontend and backend folders:
```
.git
node_modules
npm-debug.log
Dockerfile
.dockerignore
```

3. For consistent caching, you might want to add a `.docker` folder to your `.gitignore`.

4. You can use Docker extension in VS Code for better Docker management:
   - Install "Docker" extension by Microsoft
   - Right-click on your Dockerfile to build and run directly from VS Code

5. Remember that Docker Desktop on Windows uses WSL2 behind the scenes, so ensure WSL2 is properly set up.

This setup should give you a fully containerized application with the frontend and backend working together. The frontend is served by Nginx, which also handles routing API requests to your FastAPI backend.

# Dockerization Process Explained

Dockerization is the process of packaging an application and its dependencies into a standardized unit called a container. This makes the application portable and ensures it runs consistently across different environments. Let me break down the entire process I outlined for both your frontend and backend applications.

## General Dockerization Concept

1. **Create a Dockerfile**: This is a text file with instructions for building a Docker image
2. **Build an image**: This image contains your code, runtime, libraries, and everything needed to run your application
3. **Run a container**: A running instance of the image that executes your application
4. **Use Docker Compose**: For multi-container applications to define and run all services together

## Backend Dockerization (FastAPI)

### Step 1: Create a Dockerfile
The backend Dockerfile does the following:
- Starts from a Python base image (`python:3.9-slim`)
- Installs system dependencies needed for audio processing (ffmpeg, libsndfile1)
- Installs PyTorch with CUDA support for GPU acceleration
- Copies and installs Python package dependencies from requirements.txt
- Copies the application code into the container
- Creates a directory for storing transcripts
- Exposes port 8000 for the API
- Defines the command to start the FastAPI server

### Step 2: Define Dependencies
The requirements.txt file lists all Python packages needed:
- fastapi for the web framework
- uvicorn for the ASGI server
- whisper-s2t for speech-to-text processing
- Other dependencies your application uses

### Step 3: Configure Volume Mounts
The Docker Compose configuration:
- Maps your local code directory to the container for development
- Creates a persistent volume for transcripts
- Configures GPU passthrough for CUDA acceleration

## Frontend Dockerization (React/Vite)

### Step 1: Multi-stage Dockerfile
The frontend Dockerfile uses a multi-stage build:

**Build Stage:**
- Starts from Node.js image
- Copies package files and installs dependencies
- Copies application code
- Builds the React app using Vite (`npm run build`)

**Production Stage:**
- Uses a lightweight Nginx image
- Copies only the built files from the previous stage
- Configures Nginx to serve the static files
- Exposes port 80 for web traffic

### Step 2: Nginx Configuration
The nginx.conf file:
- Configures the web server to serve the React app
- Sets up routing so client-side routing works
- Configures API request proxying to the backend service

### Step 3: API Configuration
- Updates Vite config for development proxy
- Creates a configuration file to handle API URLs in different environments

## Docker Compose Integration

Docker Compose ties everything together:
- Defines both services (frontend and backend) in one file
- Sets up networking between containers
- Configures port mapping to host
- Defines dependencies between services
- Sets up volume mounts for persistent data

## The Complete Process

1. **Development**:
   - Write the Dockerfiles for each part of your application
   - Configure Docker Compose for local development
   - Run with `docker-compose up` to start everything together

2. **Building**:
   - Docker reads the Dockerfile instructions
   - Builds layers of the image (each step creates a cached layer)
   - Creates final images for both frontend and backend

3. **Running**:
   - Docker Compose creates a network for your containers
   - Starts containers based on the images
   - Sets up communication between them
   - Maps ports to your host machine

4. **Deployment**:
   - The same images can be deployed to any environment that runs Docker
   - The application will behave consistently across environments

This approach separates your application into logical components (frontend, backend) while providing a unified way to develop, test, and deploy everything together. The containerization ensures that "it works on my machine" becomes "it works everywhere."
