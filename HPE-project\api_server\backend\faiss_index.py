import faiss
import numpy as np
import os
import json
from fastapi import FastAP<PERSON>
from models import model, llm_model  # Load embedding model & LLM

app = FastAPI()

FAISS_INDEX_FILE = "index_faiss.bin"
TRANSCRIPT_DIR = "transcriptions/"
transcript_store = []  # Stores transcriptions with timestamps

def load_faiss_index():
    """Load FAISS index if it exists, otherwise create a new one."""
    if os.path.exists(FAISS_INDEX_FILE):
        index = faiss.read_index(FAISS_INDEX_FILE)
        if index.ntotal == 0:  # If empty, rebuild it
            process_transcripts()
    else:
        index = faiss.IndexFlatL2(384)
    return index

index = load_faiss_index()  # Initialize FAISS index


def process_transcripts():
    """Process transcriptions and rebuild FAISS index."""
    global index, transcript_store
    transcript_store.clear()

    transcript_texts = []
    timestamp_data = []

    if not os.path.exists(TRANSCRIPT_DIR):
        os.makedirs(TRANSCRIPT_DIR)  # Ensure directory exists

    for file in os.listdir(TRANSCRIPT_DIR):
        if file.endswith(".json"):
            with open(os.path.join(TRANSCRIPT_DIR, file), "r") as f:
                transcript_json = json.load(f)
                for segment in transcript_json:
                    transcript_texts.append(segment["text"])
                    timestamp_data.append(segment)
                    transcript_store.append(segment)  # Store transcript

    if transcript_texts:
        print(f"⚡ Found {len(transcript_texts)} transcript segments.")
        embeddings = np.array(model.encode(transcript_texts), dtype=np.float32)
        index = faiss.IndexFlatL2(embeddings.shape[1])
        index.add(embeddings)
        faiss.write_index(index, FAISS_INDEX_FILE)  # Save FAISS index
        print("✅ FAISS index created and saved!")

    return {"message": "Transcriptions indexed successfully!"}

@app.post("/process_transcripts")
async def process_transcripts_api():
    """API endpoint to process transcriptions and index them."""
    return process_transcripts()


@app.get("/search")
async def search_transcripts(query: str, top_k: int = 5):
    """Search FAISS index and return transcript segments with timestamps."""
    if index.ntotal == 0:
        return {"error": "FAISS index is empty. Process transcripts first."}

    query_embedding = np.array(model.encode([query]), dtype=np.float32)
    _, indices = index.search(query_embedding, top_k)

    results = []
    for idx in indices[0]:
        if idx == -1:  # No match found
            continue
        if idx < len(transcript_store):
            results.append({
                "text": transcript_store[idx]["text"],
                "start_time": transcript_store[idx]["start"],
                "end_time": transcript_store[idx]["end"]
            })

    return {"query": query, "results": results if results else ["No relevant match found."]}


@app.get("/summarize")
async def summarize_transcript(timestamp: float):
    """Summarize the transcript segment closest to the given timestamp."""
    if not transcript_store:
        return {"error": "No transcripts available. Process them first."}

    # Find the closest matching transcript segment
    closest_segment = min(transcript_store, key=lambda seg: abs(seg["start"] - timestamp))

    summary_prompt = f"Summarize this transcript segment:\n{closest_segment['text']}"
    summary = llm_model.invoke(summary_prompt)

    return {
        "timestamp": timestamp,
        "start_time": closest_segment["start"],
        "end_time": closest_segment["end"],
        "original_text": closest_segment["text"],
        "summary": summary
    }


@app.get("/status")
async def status():
    """Check FAISS index and transcript count."""
    return {"faiss_entries": index.ntotal, "transcripts_stored": len(transcript_store)}


# ✅ Initialize FAISS with transcriptions on startup
process_transcripts()  