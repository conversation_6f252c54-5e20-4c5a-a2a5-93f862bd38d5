#div-file-upload {
  position: relative;
  width: 600px;
  margin: 20px auto;
}

#form-file-upload {
  position: relative;
  width: 100%;
  margin: 20px auto;
}

#label-file-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #555;
  border-radius: 8px;
  padding: 40px;
  cursor: pointer;
  background-color: #1e1e1e;
  transition: border-color 0.3s ease;
}

#label-file-upload.drag-active {
  border-color: #ffffff;
}

#label-file-upload p {
  margin: 0;
  font-size: 16px;
}

.file-name {
  margin-top: 10px;
  font-size: 14px;
  color: #ccc;
}

#drag-file-element {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 10;
}

.transcription-container{
  white-space: pre-wrap;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  width: 100%;
  padding: 8px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
