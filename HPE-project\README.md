# Voice Based Video Search using RAG

- Speech to text: Whisper/whisper-s2t
- Vector Database: Weaviate
- Frontend: React
- Backend: FastAPI



## api_server

### FastAPI
[click here](api_server/README.md)

```bash
cd api_server
python server.py
```

## video_inferencing

### React app (currently just shows transcribed output with vtt timestamps)
[click here](video_inferencing/README.md)

```bash
cd video_inferencing
npm start
```