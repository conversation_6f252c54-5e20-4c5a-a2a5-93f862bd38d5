FROM pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime

# Set working directory
WORKDIR /app

# Install system dependencies in a single layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    python3-dev \
    espeak \
    ffmpeg \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip
RUN pip3 install --no-cache-dir --upgrade pip

# Copy only requirements first to leverage Docker cache
COPY server_requirements.txt ./

# Install dependencies directly from requirements
RUN pip3 install --no-cache-dir -r server_requirements.txt

# Copy the rest of the application
COPY . .

# Make the entrypoint script executable
RUN chmod +x docker-entrypoint.sh

# Set environment variables for backend connection
ENV BACKEND_EMBED_URL=http://faiss_backend:8001/embed
ENV external_service_url=http://faiss_backend:8001/chat
ENV FAISS_BACKEND_URL=http://faiss_backend:8001

# Use the entrypoint script
ENTRYPOINT ["./docker-entrypoint.sh"]

# Run server
CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8000"]








