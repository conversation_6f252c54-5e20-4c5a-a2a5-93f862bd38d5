from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import faiss
import numpy as np
import pickle
from sentence_transformers import SentenceTransformer
from youtube_transcript_api import YouTubeTranscriptApi

app = FastAPI()

# Load embedding model
model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

# FAISS index setup
dimension = 384  # MiniLM embedding size
index = faiss.IndexFlatL2(dimension)

# Load FAISS index and metadata if available
try:
    index = faiss.read_index("faiss_index.bin")  # Load FAISS index
    with open("video_utils.pkl", "rb") as f:
        video_data = pickle.load(f)  # Load metadata
except:
    video_data = {}  # Start fresh if files are missing

class VideoRequest(BaseModel):
    video_id: str

class QueryRequest(BaseModel):
    query: str

def get_video_transcript(video_id):
    """Fetch transcript for a given YouTube video ID"""
    try:
        transcript = YouTubeTranscriptApi.get_transcript(video_id)
        text = " ".join([item["text"] for item in transcript])
        return text
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Transcript error: {str(e)}")

@app.post("/add/")
def add_video(video: VideoRequest):
    """Add a YouTube video's transcript to FAISS index"""
    video_id = video.video_id.strip()
    if video_id in video_data:
        return {"message": "Video already added!"}

    transcript = get_video_transcript(video_id)
    embedding = model.encode([transcript])  # Generate embedding
    index.add(np.array(embedding, dtype=np.float32))

    video_data[video_id] = {"video_id": video_id, "transcript": transcript}

    # Save FAISS index and metadata
    faiss.write_index(index, "faiss_index.bin")
    with open("video_utils.pkl", "wb") as f:
        pickle.dump(video_data, f)

    return {"message": "Video added successfully!"}

@app.get("/search/")
def search(query: str):
    """Search FAISS index for relevant content"""
    if index.ntotal == 0:
        raise HTTPException(status_code=404, detail="No videos available.")

    query_embedding = model.encode([query])
    _, indices = index.search(np.array(query_embedding, dtype=np.float32), k=1)

    closest_video_id = list(video_data.keys())[indices[0][0]]
    return {"results": video_data[closest_video_id]}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
