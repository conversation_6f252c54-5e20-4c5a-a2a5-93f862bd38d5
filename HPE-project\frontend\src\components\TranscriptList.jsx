import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';
import { ClockIcon, FileTextIcon, ChevronRightIcon, Trash2Icon, AlertCircleIcon } from 'lucide-react';

const TranscriptList = () => {
  const [transcripts, setTranscripts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchTranscripts();
  }, []);

  const fetchTranscripts = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('http://localhost:8000/transcripts/');
      // Sort by timestamp (newest first)
      const sortedTranscripts = response.data.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
      );
      setTranscripts(sortedTranscripts);
      setIsLoading(false);
    } catch (err) {
      console.error('Error fetching transcripts:', err);
      setError('Failed to load transcripts. Please try again.');
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (e, transcriptId) => {
    e.preventDefault(); // Prevent navigating to detail page
    e.stopPropagation(); // Stop event bubbling
    setDeleteConfirm(transcriptId);
  };

  const confirmDelete = async (e, transcriptId) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      setIsDeleting(true);
      await axios.delete(`http://localhost:8000/transcripts/${transcriptId}`);
      setTranscripts(transcripts.filter(t => t.id !== transcriptId));
      setDeleteConfirm(null);
      setIsDeleting(false);
    } catch (err) {
      console.error('Error deleting transcript:', err);
      setError('Failed to delete transcript. Please try again.');
      setIsDeleting(false);
    }
  };

  const cancelDelete = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDeleteConfirm(null);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 rounded-lg mx-auto max-w-3xl mt-6">
        <h2 className="text-red-700 font-bold text-lg mb-2">Error</h2>
        <p>{error}</p>
        <button 
          onClick={() => {
            setError(null);
            fetchTranscripts();
          }}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold text-gray-800 mb-6">Your Transcripts</h2>
      
      {transcripts.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <FileTextIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No Transcripts Yet</h3>
          <p className="text-gray-500 mb-6">Upload a file to create your first transcript.</p>
          <Link to="/upload" className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors">
            Upload Audio/Video
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <ul className="divide-y divide-gray-200">
            {transcripts.map((transcript) => (
              <li key={transcript.id} className="hover:bg-gray-50">
                <Link to={`/transcripts/${transcript.id}`} className="block p-6">
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-800 mb-1 truncate">{transcript.filename}</h3>
                      <div className="flex items-center text-sm text-gray-500">
                        <ClockIcon className="w-4 h-4 mr-1" />
                        <span>{formatDate(transcript.timestamp)}</span>
                      </div>
                      {transcript.length && (
                        <p className="text-sm text-gray-500 mt-1">
                          {transcript.length} characters
                        </p>
                      )}
                    </div>
                    <div className="flex items-center">
                      {deleteConfirm === transcript.id ? (
                        <div className="flex items-center space-x-2" onClick={e => e.stopPropagation()}>
                          <button 
                            onClick={e => confirmDelete(e, transcript.id)}
                            disabled={isDeleting}
                            className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm"
                          >
                            {isDeleting ? 'Deleting...' : 'Confirm'}
                          </button>
                          <button 
                            onClick={cancelDelete}
                            className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm"
                          >
                            Cancel
                          </button>
                        </div>
                      ) : (
                        <button 
                          onClick={e => handleDeleteClick(e, transcript.id)}
                          className="p-2 text-gray-500 hover:text-red-500 transition-colors mr-2"
                        >
                          <Trash2Icon className="w-5 h-5" />
                        </button>
                      )}
                      <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TranscriptList;