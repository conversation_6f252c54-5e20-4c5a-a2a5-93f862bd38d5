from sentence_transformers import SentenceTransformer
from faster_whisper import WhisperModel
from langchain_community.llms import Ollama

# Load Sentence Transformer for FAISS embeddings
model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

# Load Whisper model for transcription
whisper_model = WhisperModel("small")  # Use "medium" or "large" for better accuracy

# Load Ollama LLM (e.g., Mistral 7B)
llm_model = Ollama(model="tinyllama")  # Change to "gemma", "llama3", etc.